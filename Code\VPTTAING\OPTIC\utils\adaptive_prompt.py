import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.ops import deform_conv2d


class AdaptivePrompt(nn.Module):
    def __init__(self, prompt_alpha=0.01, image_size=512, deform_kernel_size=3,
                 lambda_low=0.5, lambda_deform=0.05, prompt_init_std=0.02):
        super().__init__()
        
        # 低频提示参数
        self.prompt_size = int(image_size * prompt_alpha) if int(image_size * prompt_alpha) > 1 else 1
        self.padding_size = (image_size - self.prompt_size) // 2
        self.prompt_init_std = prompt_init_std

        # 使用频域适配的初始化：基于典型图像的频域幅度范围
        # 典型医学图像的低频幅度范围约为100-1000，使用0.8-1.2的范围进行调制
        self.init_para = 0.8 + 0.4 * torch.rand((1, 3, self.prompt_size, self.prompt_size))
        self.data_prompt = nn.Parameter(self.init_para, requires_grad=True)
        self.pre_prompt = self.data_prompt.detach().cpu().data
        self.lambda_low = lambda_low
        
        # 可变形卷积参数
        self.deform_kernel_size = deform_kernel_size
        self.lambda_deform = lambda_deform
        
        # 偏移量生成器（输入通道数为3，对应原始RGB图像）
        self.f_d = nn.Conv2d(3, 3, kernel_size=3, padding=1, groups=3, bias=False)
        self.f_g = nn.Conv2d(3, 2*deform_kernel_size*deform_kernel_size, kernel_size=1, bias=False)
        
        # 可变形卷积权重
        self.deform_weight = nn.Parameter(torch.randn(3, 3, deform_kernel_size, deform_kernel_size))
        
        # 初始化权重
        self._init_weights()

        # 监控信息存储
        self.debug_info = {}
    
    def _init_weights(self):
        """初始化网络权重"""
        # f_d权重初始化
        nn.init.kaiming_normal_(self.f_d.weight, mode='fan_out', nonlinearity='relu')
        
        # f_g权重初始化
        nn.init.kaiming_normal_(self.f_g.weight, mode='fan_out', nonlinearity='relu')
        
        # 可变形卷积权重初始化
        nn.init.kaiming_normal_(self.deform_weight, mode='fan_out', nonlinearity='relu')
    
    def update(self, init_data):
        """更新低频提示参数，确保数值范围合适"""
        with torch.no_grad():
            # 将内存库数据归一化到合适的范围[0.8, 1.2]
            init_data_normalized = init_data.clone()
            if init_data_normalized.std() > 0:
                # 标准化到0均值1标准差，然后缩放到合适范围
                init_data_normalized = (init_data_normalized - init_data_normalized.mean()) / init_data_normalized.std()
                init_data_normalized = 1.0 + 0.1 * init_data_normalized  # 范围约[0.7, 1.3]
                init_data_normalized = torch.clamp(init_data_normalized, 0.8, 1.2)
            else:
                # 如果标准差为0，使用默认值
                init_data_normalized = torch.ones_like(init_data_normalized)

            self.data_prompt.copy_(init_data_normalized)
    
    def iFFT(self, amp_src_, pha_src, imgH, imgW):
        """逆FFT变换"""
        # recompose fft
        real = torch.cos(pha_src) * amp_src_
        imag = torch.sin(pha_src) * amp_src_
        fft_src_ = torch.complex(real=real, imag=imag)

        src_in_trg = torch.fft.ifft2(fft_src_, dim=(-2, -1), s=[imgH, imgW]).real
        return src_in_trg
    
    def _low_frequency_prompt(self, x):
        """
        基于原始图像x进行低频提示处理
        Args:
            x: 原始输入图像 (B, 3, H, W)
        Returns:
            prompt_x: 低频提示处理后的图像 (B, 3, H, W)
            low_freq: 低频特征用于记忆库 (B, 3, prompt_size, prompt_size)
        """
        _, _, imgH, imgW = x.size()

        fft = torch.fft.fft2(x.clone(), dim=(-2, -1))

        # extract amplitude and phase of both ffts
        amp_src, pha_src = torch.abs(fft), torch.angle(fft)
        amp_src = torch.fft.fftshift(amp_src)

        # obtain the low frequency amplitude part
        prompt = F.pad(self.data_prompt, [self.padding_size, imgH - self.padding_size - self.prompt_size,
                                          self.padding_size, imgW - self.padding_size - self.prompt_size],
                       mode='constant', value=1.0).contiguous()

        # 调试信息：分析频域操作的有效性
        amp_center_before = amp_src[:, :, self.padding_size:self.padding_size+self.prompt_size,
                                   self.padding_size:self.padding_size+self.prompt_size]

        self.debug_prompt_stats = {
            'data_prompt_mean': self.data_prompt.mean().item(),
            'data_prompt_std': self.data_prompt.std().item(),
            'data_prompt_range': f"[{self.data_prompt.min().item():.3f}, {self.data_prompt.max().item():.3f}]",
            'amp_src_global_mean': amp_src.mean().item(),
            'amp_center_before_mean': amp_center_before.mean().item(),
            'prompt_center_mean': self.data_prompt.mean().item(),
            'frequency_domain_match': amp_center_before.mean().item() / (self.data_prompt.mean().item() + 1e-8)
        }

        amp_src_before = amp_src.clone()
        amp_src_ = amp_src * prompt

        # 调试信息：分析频域中心区域的变化
        amp_center_after = amp_src_[:, :, self.padding_size:self.padding_size+self.prompt_size,
                                   self.padding_size:self.padding_size+self.prompt_size]

        center_change_abs = (amp_center_after.mean() - amp_center_before.mean()).abs().item()
        center_change_rel = center_change_abs / (amp_center_before.mean().item() + 1e-8)

        self.debug_prompt_stats.update({
            'amp_center_after_mean': amp_center_after.mean().item(),
            'center_change_absolute': center_change_abs,
            'center_change_relative': center_change_rel,
            'center_preservation_ratio': amp_center_after.mean().item() / (amp_center_before.mean().item() + 1e-8)
        })

        amp_src_ = torch.fft.ifftshift(amp_src_)

        amp_low_ = amp_src[:, :, self.padding_size:self.padding_size+self.prompt_size,
                                 self.padding_size:self.padding_size+self.prompt_size]

        prompt_x = self.iFFT(amp_src_, pha_src, imgH, imgW)

        # 调试信息：分析空域变化
        spatial_change = (prompt_x - x).abs().mean().item()
        self.debug_prompt_stats['spatial_change'] = spatial_change

        return prompt_x, amp_low_
    
    def _generate_offset(self, x):
        """
        基于原始图像x生成偏移量
        Args:
            x: 原始输入图像 (B, 3, H, W)
        Returns:
            offset: 偏移量 (B, 2*K*K, H, W)
        """
        # f_d: 深度可分离卷积，保持通道独立
        x_depth = self.f_d(x)  # (B, 3, H, W) → (B, 3, H, W)
        
        # f_g: 跨通道融合，生成偏移量
        offset = self.f_g(x_depth)  # (B, 3, H, W) → (B, 2*K*K, H, W)

        # 添加范围限制，防止采样越界
        offset = torch.tanh(offset) * 2.0  # 限制在[-2, 2]范围内

        return offset
    
    def forward(self, x):
        """
        并联处理：同时应用低频提示和可变形卷积
        Args:
            x: 原始输入图像 (B, 3, H, W)
        Returns:
            adapted_x: 自适应处理后的图像 (B, 3, H, W)
            low_freq: 低频特征用于记忆库
        """
        # 分支1：低频提示处理
        low_freq_prompt, low_freq = self._low_frequency_prompt(x)
        
        # 分支2：可变形卷积处理
        offset = self._generate_offset(x)
        deform_prompt = deform_conv2d(x, offset, self.deform_weight, 
                                      padding=self.deform_kernel_size//2)
        
        # 融合策略：原图像 + 低频提示增量 + 可变形提示
        low_freq_delta = low_freq_prompt - x  # 低频提示的增量部分
        adapted_x = x + self.lambda_low * low_freq_delta + self.lambda_deform * deform_prompt

        # 监控信息收集
        # 1. 偏移量统计
        offset_stats = {
            'mean': offset.mean().item(),
            'std': offset.std().item(),
            'max_abs': offset.abs().max().item()
        }

        # 2. 两分支贡献度监控
        low_freq_magnitude = low_freq_delta.abs().mean().item()
        deform_magnitude = deform_prompt.abs().mean().item()

        # 3. 融合后的总变化监控
        total_change = (adapted_x - x).abs().mean().item()

        # 4. 新增分支平衡度监控
        balance_ratio = low_freq_magnitude / (deform_magnitude + 1e-8)
        healthy_balance = 0.1 <= balance_ratio <= 10.0

        # 5. 数值稳定性检查
        offset_healthy = offset_stats['max_abs'] < 5.0

        # 6. 存储增强的监控信息
        self.debug_info = {
            'offset_stats': offset_stats,
            'low_freq_magnitude': low_freq_magnitude,
            'deform_magnitude': deform_magnitude,
            'total_change': total_change,
            'contribution_ratio': deform_magnitude / (low_freq_magnitude + 1e-8),
            'balance_ratio': balance_ratio,
            'healthy_balance': healthy_balance,
            'offset_healthy': offset_healthy,
            'prompt_debug': self.debug_prompt_stats  # 添加低频提示的调试信息
        }

        return adapted_x, low_freq
